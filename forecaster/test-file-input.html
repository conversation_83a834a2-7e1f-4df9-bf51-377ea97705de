<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Input Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #f0f0f0;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>File Input Test</h1>
    <p>This page tests basic file input functionality to debug the "Choose File" button issue.</p>

    <div class="test-section">
        <h2>Test 1: Basic File Input</h2>
        <input type="file" id="basicFileInput" accept=".gpx" />
        <button onclick="triggerBasicFileInput()">Trigger Basic File Input</button>
    </div>

    <div class="test-section">
        <h2>Test 2: Hidden File Input (like in the app)</h2>
        <input type="file" id="hiddenFileInput" accept=".gpx" style="display: none;" />
        <button onclick="triggerHiddenFileInput()">Trigger Hidden File Input</button>
    </div>

    <div class="test-section">
        <h2>Test 3: File Input with Ref (React-like)</h2>
        <input type="file" id="refFileInput" accept=".gpx" style="display: none;" />
        <button onclick="triggerRefFileInput()">Trigger Ref File Input</button>
    </div>

    <div class="test-section">
        <h2>Test 4: Multiple Event Listeners</h2>
        <input type="file" id="multiEventFileInput" accept=".gpx" style="display: none;" />
        <button onclick="triggerMultiEventFileInput()">Trigger Multi Event File Input</button>
    </div>

    <div class="log" id="log"></div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function triggerBasicFileInput() {
            log('Triggering basic file input...');
            const input = document.getElementById('basicFileInput');
            if (input) {
                input.click();
                log('Basic file input click triggered');
            } else {
                log('ERROR: Basic file input not found');
            }
        }

        function triggerHiddenFileInput() {
            log('Triggering hidden file input...');
            const input = document.getElementById('hiddenFileInput');
            if (input) {
                input.click();
                log('Hidden file input click triggered');
            } else {
                log('ERROR: Hidden file input not found');
            }
        }

        function triggerRefFileInput() {
            log('Triggering ref file input...');
            const input = document.getElementById('refFileInput');
            if (input) {
                log('File input element found, triggering click...');
                input.click();
                log('Ref file input click triggered');
            } else {
                log('ERROR: Ref file input not found');
            }
        }

        function triggerMultiEventFileInput() {
            log('Triggering multi event file input...');
            const input = document.getElementById('multiEventFileInput');
            if (input) {
                log('Adding multiple event listeners...');
                input.addEventListener('click', () => log('Click event fired'));
                input.addEventListener('change', (e) => {
                    log(`Change event fired: ${e.target.files?.length || 0} files selected`);
                    if (e.target.files && e.target.files.length > 0) {
                        log(`Selected file: ${e.target.files[0].name}`);
                    }
                });
                input.click();
                log('Multi event file input click triggered');
            } else {
                log('ERROR: Multi event file input not found');
            }
        }

        // Add change listeners to all file inputs
        document.addEventListener('DOMContentLoaded', () => {
            log('Page loaded, setting up event listeners...');
            
            ['basicFileInput', 'hiddenFileInput', 'refFileInput', 'multiEventFileInput'].forEach(id => {
                const input = document.getElementById(id);
                if (input) {
                    input.addEventListener('change', (e) => {
                        log(`${id} changed: ${e.target.files?.length || 0} files selected`);
                        if (e.target.files && e.target.files.length > 0) {
                            log(`Selected file: ${e.target.files[0].name} (${e.target.files[0].size} bytes)`);
                        }
                    });
                    log(`Event listener added to ${id}`);
                } else {
                    log(`ERROR: Could not find ${id}`);
                }
            });
        });

        // Test browser compatibility
        log('Browser info:');
        log(`User Agent: ${navigator.userAgent}`);
        log(`Platform: ${navigator.platform}`);
        log(`Touch support: ${'ontouchstart' in window}`);
        log(`Max touch points: ${navigator.maxTouchPoints || 0}`);
    </script>
</body>
</html>
