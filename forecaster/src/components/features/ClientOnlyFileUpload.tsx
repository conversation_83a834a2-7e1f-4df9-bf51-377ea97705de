"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { FileUpload as OriginalFileUpload } from './FileUpload';
import { Route } from '@/types';

interface FileUploadProps {
  onRouteUploaded: (route: Route) => void;
  isLoading?: boolean;
  className?: string;
}

// Client-only wrapper to prevent hydration mismatch
export function FileUpload({ onRouteUploaded, isLoading = false, className }: FileUploadProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    console.log('ClientOnlyFileUpload: Component mounting...');
    setIsMounted(true);

    // Add a small delay to ensure smooth transition
    const timer = setTimeout(() => {
      setIsInitializing(false);
      console.log('ClientOnlyFileUpload: Initialization complete');
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Show loading state during initialization
  if (!isMounted || isInitializing) {
    return (
      <Card className={cn('w-full', className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload GPX File
          </CardTitle>
          <CardDescription>
            Upload your GPX file to get started with weather analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed rounded-lg p-8 text-center transition-colors border-muted-foreground/25">
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-4">
              Drag and drop your GPX file here, or click to browse
            </p>
            <Button variant="outline" disabled className="relative">
              {isInitializing && isMounted ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Initializing...
                </>
              ) : (
                'Choose File'
              )}
            </Button>
            <p className="text-xs text-muted-foreground mt-4">
              Supports GPX files up to 5 MB
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render the actual component only on client
  console.log('ClientOnlyFileUpload: Rendering actual FileUpload component');
  return <OriginalFileUpload onRouteUploaded={onRouteUploaded} isLoading={isLoading} className={className} />;
}
